import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/progress_item_card.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class QuestionPage extends StatefulWidget {
  final entities.Form form;

  const QuestionPage({
    super.key,
    required this.form,
  });

  @override
  State<QuestionPage> createState() => _QuestionPageState();
}

class _QuestionPageState extends State<QuestionPage> {
  // Get questions from the form
  List<entities.Question>? get questionItems => widget.form.questions;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: widget.form.formName ?? 'Questions',
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(8),
            questionItems == null || questionItems!.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'No questions available for this form',
                        style: textTheme.bodyLarge,
                      ),
                    ),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 8.0),
                    itemCount: questionItems!.length,
                    itemBuilder: (context, index) {
                      final question = questionItems![index];

                      // Calculate progress based on measurements if available
                      double progress = 0.0;
                      String progressText = '0 of 0';

                      if (question.measurements != null &&
                          question.measurements!.isNotEmpty) {
                        int totalMeasurements = question.measurements!.length;
                        // For now, we don't have a way to track answered measurements
                        // This could be enhanced later with actual progress tracking

                        progressText = '0 of $totalMeasurements';
                      }

                      return ProgressItemCard(
                        title:
                            question.questionDescription ?? 'Unnamed Question',
                        progress: progress,
                        progressText: progressText,
                        width: 1.0, // Full width for list view
                      );
                    },
                  ),
            const Gap(24),
          ],
        ),
      ),
    );
  }
}
